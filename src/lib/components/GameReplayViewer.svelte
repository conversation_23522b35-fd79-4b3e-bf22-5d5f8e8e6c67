<!--
  GameReplayViewer Component
  
  Interactive game replay showing step-by-step move visualization:
  - Navigate through moves with controls
  - See board state before/after each move
  - Highlight selected tiles for each move
  - Show move details and scoring
-->

<script lang="ts">
	import BoardVisualization from './BoardVisualization.svelte';
	import type { EnhancedMove } from '$lib/types';

	interface Props {
		/** Array of enhanced moves to replay */
		moves: EnhancedMove[];
		/** Initial board state */
		initialBoard?: any[][];
		/** Whether to auto-play the replay */
		autoPlay?: boolean;
		/** Auto-play speed in milliseconds */
		autoPlaySpeed?: number;
	}

	let { moves, initialBoard, autoPlay = false, autoPlaySpeed = 2000 }: Props = $props();

	// Validation functions
	function isValidBoardState(board: any[][]): boolean {
		if (!board || !Array.isArray(board)) return false;
		if (board.length !== 5) return false;
		return board.every((row) => Array.isArray(row) && row.length === 5);
	}

	// Fix positions for a word to ensure they correspond to the word letters
	function fixPositionsForWord(word: string, board: any[][]): Array<[number, number]> | null {
		if (!isValidBoardState(board)) return null;

		const letters = word.split('');
		const positions: Array<[number, number]> = [];
		const usedPositions = new Set<string>();

		// Build a map of available tiles for each letter
		const letterTileMap: Record<string, Array<{ row: number; col: number; tile: any }>> = {};
		for (let row = 0; row < 5; row++) {
			for (let col = 0; col < 5; col++) {
				const tile = board[row][col];
				if (tile && tile.letter) {
					if (!letterTileMap[tile.letter]) {
						letterTileMap[tile.letter] = [];
					}
					letterTileMap[tile.letter].push({ row, col, tile });
				}
			}
		}

		// Sort tiles by multiplier value (best first) for each letter
		for (const letter in letterTileMap) {
			letterTileMap[letter].sort((a, b) => {
				const aValue = (a.tile.letterMult || 1) * (a.tile.wordMult || 1);
				const bValue = (b.tile.letterMult || 1) * (b.tile.wordMult || 1);
				return bValue - aValue; // Descending order (best first)
			});
		}

		// For each letter in the word (in order), find the best available tile
		for (let i = 0; i < letters.length; i++) {
			const letter = letters[i];
			const availableTiles = letterTileMap[letter];
			if (!availableTiles || availableTiles.length === 0) {
				return null; // Letter not available on board
			}

			// Find the best unused tile for this letter
			let bestTile: { row: number; col: number; tile: any } | null = null;
			for (const tileInfo of availableTiles) {
				const posKey = `${tileInfo.row},${tileInfo.col}`;
				if (!usedPositions.has(posKey)) {
					bestTile = tileInfo;
					break; // Take the first (best) available tile
				}
			}

			if (!bestTile) {
				return null; // No unused tiles available for this letter
			}

			positions.push([bestTile.row, bestTile.col]);
			usedPositions.add(`${bestTile.row},${bestTile.col}`);
		}

		return positions;
	}

	function validateSelectedPositions(positions: Array<[number, number]>, board: any[][]): boolean {
		if (!positions || !Array.isArray(positions)) return false;
		if (!isValidBoardState(board)) return false;

		return positions.every(([row, col]) => {
			return row >= 0 && row < 5 && col >= 0 && col < 5 && board[row] && board[row][col];
		});
	}

	// Check if multipliers are preserved correctly in board state
	function validateMultiplierConsistency(board: any[][]): {
		isConsistent: boolean;
		issues: string[];
	} {
		const issues: string[] = [];

		if (!isValidBoardState(board)) {
			return { isConsistent: false, issues: ['Invalid board state'] };
		}

		// Check that all tiles have proper multiplier values
		for (let row = 0; row < 5; row++) {
			for (let col = 0; col < 5; col++) {
				const tile = board[row][col];
				if (!tile) {
					issues.push(`Missing tile at [${row},${col}]`);
					continue;
				}

				// Validate multiplier values
				if (typeof tile.letterMult !== 'number' || tile.letterMult < 1 || tile.letterMult > 3) {
					issues.push(`Invalid letterMult at [${row},${col}]: ${tile.letterMult}`);
				}
				if (typeof tile.wordMult !== 'number' || tile.wordMult < 1 || tile.wordMult > 3) {
					issues.push(`Invalid wordMult at [${row},${col}]: ${tile.wordMult}`);
				}

				// Check position consistency
				if (tile.row !== row || tile.col !== col) {
					issues.push(
						`Position mismatch at [${row},${col}]: tile reports [${tile.row},${tile.col}]`
					);
				}
			}
		}

		return { isConsistent: issues.length === 0, issues };
	}

	// Debug: Log the moves data structure when component loads and validate positions
	$effect(() => {
		console.log('[GameReplayViewer] Component loaded with moves:', {
			moveCount: moves.length,
			moves: moves.map((move, index) => ({
				index,
				word: move.word,
				score: move.score,
				positions: move.positions,
				hasBoardBefore: !!move.boardBefore,
				hasBoardAfter: !!move.boardAfter,
				boardBeforeFirstTile: move.boardBefore?.[0]?.[0]?.letter,
				boardAfterFirstTile: move.boardAfter?.[0]?.[0]?.letter
			}))
		});

		// Validate and potentially fix position mismatches
		moves.forEach((move, index) => {
			if (move.boardBefore && move.positions) {
				const extractedLetters = move.positions
					.map(([row, col]) => {
						const tile = move.boardBefore?.[row]?.[col];
						return tile ? tile.letter : '?';
					})
					.join('');

				if (extractedLetters !== move.word) {
					console.warn(
						`[GameReplayViewer] Position mismatch detected for move ${index}: word="${move.word}", extracted="${extractedLetters}"`
					);
				}
			}
		});
	});

	// Current move index (-1 = initial state, 0+ = after move N)
	let currentMoveIndex = $state(-1);
	let isPlaying = $state(false);
	let playInterval: ReturnType<typeof setInterval> | null = null;

	// Board state to display - shows the board BEFORE the move with selected tiles highlighted
	let displayBoard = $derived(() => {
		if (currentMoveIndex === -1) {
			// Show initial board state
			const board = initialBoard || moves[0]?.boardBefore || [];
			const multiplierCheck = validateMultiplierConsistency(board);
			console.log('[GameReplayViewer] displayBoard: initial state', {
				currentMoveIndex,
				boardSize: board.length,
				firstTile: board[0]?.[0]?.letter,
				isValidBoard: isValidBoardState(board),
				multipliersConsistent: multiplierCheck.isConsistent,
				multiplierIssues: multiplierCheck.issues
			});
			return board;
		} else if (currentMoveIndex < moves.length) {
			const move = moves[currentMoveIndex];

			// When viewing a move, show the board state BEFORE that move was played
			// This ensures all tiles (both selected and non-selected) show their original positions
			const board = move?.boardBefore || [];
			const multiplierCheck = validateMultiplierConsistency(board);
			console.log('[GameReplayViewer] displayBoard: move', {
				currentMoveIndex,
				moveWord: move?.word,
				hasBoardBefore: !!move?.boardBefore,
				hasBoardAfter: !!move?.boardAfter,
				boardSize: board.length,
				firstTile: board[0]?.[0]?.letter,
				selectedPositions: move?.positions,
				isValidBoard: isValidBoardState(board),
				positionsValid: validateSelectedPositions(move?.positions || [], board),
				multipliersConsistent: multiplierCheck.isConsistent,
				multiplierIssues: multiplierCheck.issues
			});
			return board;
		} else {
			// Show final state (after all moves)
			const board = moves[moves.length - 1]?.boardAfter || [];
			console.log('[GameReplayViewer] displayBoard: final state', {
				currentMoveIndex,
				boardSize: board.length,
				isValidBoard: isValidBoardState(board)
			});
			return board;
		}
	});

	// Current move being highlighted
	let currentMove = $derived(() => {
		if (currentMoveIndex >= 0 && currentMoveIndex < moves.length) {
			return moves[currentMoveIndex];
		}
		return null;
	});

	// Selected positions for current move (with runtime position fixing)
	let selectedPositions = $derived(() => {
		const move = currentMove();
		if (!move) return [];

		const board = displayBoard();
		if (!move.positions || !isValidBoardState(board)) {
			return move.positions || [];
		}

		// Check if positions match the word
		const extractedLetters = move.positions
			.map(([row, col]) => {
				const tile = board[row]?.[col];
				return tile ? tile.letter : '?';
			})
			.join('');

		if (extractedLetters === move.word) {
			// Positions are correct, use them as-is
			return move.positions;
		} else {
			// Positions are incorrect, try to fix them
			console.warn(
				`[GameReplayViewer] Fixing positions for word "${move.word}": expected "${move.word}", got "${extractedLetters}"`
			);
			const fixedPositions = fixPositionsForWord(move.word, board);
			if (fixedPositions) {
				console.log(`[GameReplayViewer] Fixed positions for "${move.word}":`, fixedPositions);
				return fixedPositions;
			} else {
				console.error(`[GameReplayViewer] Could not fix positions for word "${move.word}"`);
				return move.positions; // Fall back to original positions
			}
		}
	});

	// Navigation functions
	function goToMove(index: number) {
		currentMoveIndex = Math.max(-1, Math.min(moves.length - 1, index));
	}

	function nextMove() {
		if (currentMoveIndex < moves.length - 1) {
			currentMoveIndex++;
		} else if (isPlaying) {
			// Loop back to start when auto-playing
			currentMoveIndex = -1;
		}
	}

	function prevMove() {
		if (currentMoveIndex > -1) {
			currentMoveIndex--;
		}
	}

	function togglePlay() {
		isPlaying = !isPlaying;

		if (isPlaying) {
			playInterval = setInterval(nextMove, autoPlaySpeed);
		} else {
			if (playInterval) {
				clearInterval(playInterval);
				playInterval = null;
			}
		}
	}

	// Cleanup on destroy
	function cleanup() {
		if (playInterval) {
			clearInterval(playInterval);
		}
	}

	// Auto-start if requested
	$effect(() => {
		if (autoPlay && !isPlaying) {
			togglePlay();
		}

		return cleanup;
	});

	// Calculate cumulative score up to current move
	let cumulativeScore = $derived(() => {
		if (currentMoveIndex < 0) return 0;
		return moves.slice(0, currentMoveIndex + 1).reduce((sum, move) => sum + move.score, 0);
	});
</script>

<div class="game-replay-viewer rounded-lg bg-white p-6 shadow-lg">
	<!-- Header -->
	<div class="mb-6 flex items-center justify-between">
		<h3 class="text-xl font-semibold text-gray-900">Game Replay</h3>
		<div class="text-sm text-gray-600">
			{moves.length} moves • {moves.reduce((sum, move) => sum + move.score, 0)} total points
		</div>
	</div>

	<!-- Current Move Info -->
	<div class="mb-6 rounded-lg bg-gray-50 p-4">
		{#if currentMoveIndex === -1}
			<div class="text-center">
				<h4 class="font-medium text-gray-900">Initial Board State</h4>
				<p class="text-sm text-gray-600">Ready to start the game</p>
			</div>
		{:else}
			{@const move = currentMove()}
			{#if move}
				<div class="grid grid-cols-1 gap-4 text-center md:grid-cols-3">
					<div>
						<div class="text-2xl font-bold text-blue-600">{move.word}</div>
						<div class="text-sm text-gray-600">Move {currentMoveIndex + 1}</div>
					</div>
					<div>
						<div class="text-2xl font-bold text-green-600">{move.score}</div>
						<div class="text-sm text-gray-600">Points</div>
					</div>
					<div>
						<div class="text-2xl font-bold text-purple-600">{cumulativeScore()}</div>
						<div class="text-sm text-gray-600">Total Score</div>
					</div>
				</div>

				<!-- Tile positions -->
				<div class="mt-3 text-center">
					<div class="text-sm text-gray-600">
						Selected tiles: {move.positions.map(([r, c]) => `[${r},${c}]`).join(', ')}
					</div>
				</div>
			{/if}
		{/if}
	</div>

	<!-- Board Display -->
	<div class="mb-6">
		{#if true}
			{@const board = displayBoard()}
			{@const positions = selectedPositions()}
			{@const boardValid = isValidBoardState(board)}
			{@const positionsValid = validateSelectedPositions(positions, board)}

			{#if boardValid}
				<BoardVisualization
					{board}
					selectedPositions={positions}
					showMultipliers={true}
					tileSize={56}
					animated={true}
					class="mx-auto"
				/>

				{#if !positionsValid && positions.length > 0}
					<div class="mt-2 text-center text-sm text-amber-600">
						⚠️ Some selected positions may not match the current board state
					</div>
				{/if}
			{:else}
				<div class="py-8 text-center text-gray-500">
					<p>Board state not available</p>
					<p class="mt-1 text-xs">This may indicate missing replay data</p>
				</div>
			{/if}
		{/if}
	</div>

	<!-- Controls -->
	<div class="flex items-center justify-center space-x-4">
		<!-- Previous Move -->
		<button
			onclick={prevMove}
			disabled={currentMoveIndex <= -1}
			class="rounded-lg border border-gray-300 p-2 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
			title="Previous Move"
		>
			<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
			</svg>
		</button>

		<!-- Play/Pause -->
		<button
			onclick={togglePlay}
			class="rounded-lg bg-blue-600 p-3 text-white transition-colors hover:bg-blue-700"
			title={isPlaying ? 'Pause' : 'Play'}
		>
			{#if isPlaying}
				<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6" />
				</svg>
			{:else}
				<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
					/>
				</svg>
			{/if}
		</button>

		<!-- Next Move -->
		<button
			onclick={nextMove}
			disabled={currentMoveIndex >= moves.length - 1}
			class="rounded-lg border border-gray-300 p-2 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
			title="Next Move"
		>
			<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
			</svg>
		</button>
	</div>

	<!-- Move Timeline -->
	<div class="mt-6">
		<div class="flex items-center justify-center space-x-2">
			<!-- Initial state -->
			<button
				onclick={() => goToMove(-1)}
				class="h-3 w-3 rounded-full border-2 transition-colors {currentMoveIndex === -1
					? 'border-blue-600 bg-blue-600'
					: 'border-gray-300 hover:border-gray-400'}"
				title="Initial State"
			></button>

			<!-- Move indicators -->
			{#each moves as move, index}
				<button
					onclick={() => goToMove(index)}
					class="h-3 w-3 rounded-full border-2 transition-colors {currentMoveIndex === index
						? 'border-blue-600 bg-blue-600'
						: 'border-gray-300 hover:border-gray-400'}"
					title="Move {index + 1}: {move.word} ({move.score} pts)"
				></button>
			{/each}
		</div>

		<!-- Timeline labels -->
		<div class="mt-2 flex justify-between text-xs text-gray-500">
			<span>Start</span>
			<span>Move {moves.length}</span>
		</div>
	</div>
</div>
