/**
 * Main solver implementation for the Letters game
 *
 * This module implements the core solving algorithm using beam search
 * with priority queue and branch-and-bound optimization.
 */

import TinyQueue from 'tinyqueue';
import type { Page } from '@playwright/test';
import { GameState } from '../models/GameState';
import { Board } from '../models/Board';
import { Word } from '../models/Word';
import {
	GAME_CONFIG,
	type GameNode,
	type BestLineResult,
	type EnhancedMove,
	type Word as WordInterface,
	wordToEnhancedMove
} from '../types';
import { scrapeBoard, playWord, undoLastMove } from './index';
import { hashGameState, BoardHashSet } from './hashing';
import { upperBoundForGameState, shouldPruneNode, type UpperBoundConfig } from './optimization';
import { bestWords } from '../bestWords';

/**
 * Solver configuration
 */
interface SolverConfig {
	/** Beam width parameter K */
	beamWidth: number;
	/** Maximum search depth */
	maxDepth: number;
	/** Time limit in milliseconds */
	timeLimit: number;
	/** Enable branch-and-bound pruning */
	usePruning: boolean;
	/** Enable state deduplication */
	useDeduplication: boolean;
	/** Maximum words to consider per position */
	maxWordsPerPosition: number;
	/** Enable debug logging */
	debug: boolean;
}

/**
 * Default solver configuration
 */
const DEFAULT_CONFIG: SolverConfig = {
	beamWidth: 60,
	maxDepth: GAME_CONFIG.MAX_TURNS,
	timeLimit: 180000, // 3 minutes
	usePruning: true,
	useDeduplication: true,
	maxWordsPerPosition: 100,
	debug: false
};

/**
 * Convert SolverConfig to UpperBoundConfig
 */
function toUpperBoundConfig(config: SolverConfig): UpperBoundConfig {
	return {
		includeLetterMultipliers: true,
		includeWordMultipliers: true,
		maxWordLength: 8,
		optimisticPlacement: true,
		useCache: true
	};
}

/**
 * Solver statistics
 */
interface SolverStats {
	nodesExplored: number;
	nodesPruned: number;
	statesDeduped: number;
	timeElapsed: number;
	bestScore: number;
	solutionDepth: number;
}

/**
 * Prepare the game page for scraping by handling navigation and button clicks
 */
async function prepareGamePage(page: Page): Promise<void> {
	try {
		// Wait for page to load
		await page.waitForTimeout(3000);

		// Look for and click "Play on Web" button if it exists
		const playButton = await page.$('button:has-text("Play on Web")');
		if (playButton) {
			console.log('[prepareGamePage] Found "Play on Web" button, clicking...');
			await playButton.click();
			await page.waitForTimeout(5000); // Wait for game to load
		} else {
			console.log('[prepareGamePage] "Play on Web" button not found, proceeding...');
		}

		// Ensure page is ready
		await page.waitForFunction(() => document.readyState === 'complete');
	} catch (error) {
		console.warn('[prepareGamePage] Error preparing game page:', error);
		// Continue anyway - the scraping logic will handle missing elements
	}
}

/**
 * Solve the daily board using beam search with branch-and-bound
 */
export async function solveDailyBoard(
	page: Page,
	config: SolverConfig = DEFAULT_CONFIG
): Promise<BestLineResult> {
	console.log('[solveDailyBoard] Starting solver');
	const startTime = Date.now();
	const stats: SolverStats = {
		nodesExplored: 0,
		nodesPruned: 0,
		statesDeduped: 0,
		timeElapsed: 0,
		bestScore: 0,
		solutionDepth: 0
	};

	try {
		// Ensure we're on the game page and ready to play
		if (config.debug) console.log('[solveDailyBoard] Preparing game page...');
		await prepareGamePage(page);

		// Scrape initial board state
		if (config.debug) console.log('[solveDailyBoard] Scraping initial board...');
		const initialBoard = await scrapeBoard(page);
		console.log('[solveDailyBoard] Initial board scraped:', initialBoard);
		const initialState = GameState.fromBoard(initialBoard);
		console.log('[solveDailyBoard] Initial state created:', initialState);

		// Initialize search
		console.log('[solveDailyBoard] Starting beam search...');
		const result = await beamSearch(page, initialState, config, stats);
		console.log(
			'[solveDailyBoard] Beam search completed with result:',
			JSON.stringify(result, null, 2)
		);

		// Calculate final statistics
		stats.timeElapsed = Date.now() - startTime;
		stats.bestScore = result.total;
		stats.solutionDepth = result.words.length;

		if (config.debug) {
			console.log('[solveDailyBoard] Search completed:', stats);
		}

		return result;
	} catch (error) {
		console.error('[solveDailyBoard] Error during solving:', error);
		throw error;
	}
}

/**
 * Beam search implementation with priority queue
 */
async function beamSearch(
	page: Page,
	initialState: GameState,
	config: SolverConfig,
	stats: SolverStats
): Promise<BestLineResult> {
	// Priority queue for beam search (max-heap by total + upper bound)
	const queue = new TinyQueue<GameNode>([], (a, b) => {
		const scoreA = a.total + a.upperBound;
		const scoreB = b.total + b.upperBound;
		return scoreB - scoreA; // Max-heap
	});

	// State deduplication
	const visitedStates = new BoardHashSet();

	// Best solution found so far
	let bestSolution: BestLineResult = {
		total: 0,
		words: [],
		perRound: []
	};

	// Initialize with root node
	let upperBound = 0;
	try {
		upperBound = upperBoundForGameState(initialState, toUpperBoundConfig(config));
		console.log('[beamSearch] Upper bound calculated:', upperBound);
	} catch (error) {
		console.error('[beamSearch] Error calculating upper bound:', error);
		upperBound = 1000; // Fallback value
	}

	const rootNode: GameNode = {
		board: initialState.board,
		turn: initialState.turn,
		total: initialState.total,
		moves: initialState.moves,
		upperBound: upperBound
	};

	queue.push(rootNode);
	console.log('[beamSearch] Root node added to queue');

	const startTime = Date.now();

	while (queue.length > 0) {
		console.log(`[beamSearch] Queue length: ${queue.length}`);
		// Check time limit
		if (Date.now() - startTime > config.timeLimit) {
			if (config.debug) console.log('[beamSearch] Time limit reached');
			break;
		}

		// Get next node
		const currentNode = queue.pop()!;
		stats.nodesExplored++;
		console.log(
			`[beamSearch] Processing node: turn=${currentNode.turn}, total=${currentNode.total}`
		);

		// Check if this is a complete solution
		if (currentNode.turn >= GAME_CONFIG.MAX_TURNS) {
			console.log('[beamSearch] Node is complete solution');
			if (currentNode.total > bestSolution.total) {
				// Try to validate and correct the solution for sequential replay
				const correctedSolution = await validateAndCorrectSolution(
					currentNode.moves,
					initialState.board
				);

				if (correctedSolution) {
					// Use the corrected solution
					const enhancedMoves = await createEnhancedMovesFromSolution(
						correctedSolution.moves,
						initialState.board
					);

					bestSolution = {
						total: correctedSolution.total,
						words: correctedSolution.moves.map((move) => move.letters),
						perRound: correctedSolution.moves.map((move) => ({
							word: move.letters,
							score: move.score
						})),
						enhancedMoves: enhancedMoves
					};

					if (config.debug) {
						console.log(`[beamSearch] New best solution (corrected): ${bestSolution.total}`);
					}
				} else {
					// Fall back to original solution with position mismatches preserved
					console.warn(
						'[beamSearch] Could not correct solution, using original with position mismatches'
					);
					const enhancedMoves = await createEnhancedMovesFromSolution(
						currentNode.moves,
						initialState.board
					);

					bestSolution = {
						total: currentNode.total,
						words: currentNode.moves.map((move) => move.letters),
						perRound: currentNode.moves.map((move) => ({
							word: move.letters,
							score: move.score
						})),
						enhancedMoves: enhancedMoves
					};

					if (config.debug) {
						console.log(`[beamSearch] New best solution (uncorrected): ${bestSolution.total}`);
					}
				}
			}
			continue;
		}

		// Pruning check
		if (
			config.usePruning &&
			shouldPruneNode(
				new GameState(
					currentNode.board,
					currentNode.turn,
					currentNode.total,
					currentNode.moves.map((move) => (move instanceof Word ? move : Word.fromObject(move)))
				),
				bestSolution.total,
				toUpperBoundConfig(config)
			)
		) {
			stats.nodesPruned++;
			continue;
		}

		// Deduplication check
		if (config.useDeduplication) {
			if (visitedStates.has(currentNode.board)) {
				stats.statesDeduped++;
				continue;
			}
			visitedStates.add(currentNode.board);
		}

		// Generate successor nodes
		const successors = await generateSuccessors(page, currentNode, config);

		// Add successors to queue (beam search will naturally limit the beam width)
		for (const successor of successors) {
			queue.push(successor);
		}

		// Maintain beam width by keeping only top K nodes
		if (queue.length > config.beamWidth) {
			// Convert to array, sort, and keep top K
			const nodes = [];
			while (queue.length > 0) {
				nodes.push(queue.pop()!);
			}

			// Sort by total + upper bound (descending)
			nodes.sort((a, b) => b.total + b.upperBound - (a.total + a.upperBound));

			// Keep only top K nodes
			for (let i = 0; i < Math.min(config.beamWidth, nodes.length); i++) {
				queue.push(nodes[i]);
			}
		}
	}

	return bestSolution;
}

/**
 * Validate and correct a solution to ensure it can be replayed sequentially
 * This fixes position mismatches by finding valid positions for each word on the correct board state
 */
async function validateAndCorrectSolution(
	moves: Word[],
	initialBoard: Board
): Promise<{ moves: Word[]; total: number } | null> {
	const correctedMoves: Word[] = [];
	let currentGameState = new GameState(initialBoard, 0, 0, []);
	let totalScore = 0;

	console.log('[validateAndCorrectSolution] Starting solution validation and correction');

	for (let i = 0; i < moves.length; i++) {
		const originalMove = moves[i];
		console.log(`[validateAndCorrectSolution] Processing move ${i}: ${originalMove.letters}`);

		// Check if the original positions are valid on the current board state
		const expectedLetters = originalMove.positions
			.map(([row, col]) => {
				const tile = currentGameState.board.getTile(row, col);
				return tile ? tile.letter : '?';
			})
			.join('');

		if (expectedLetters === originalMove.letters) {
			// Original positions are valid, use them as-is
			console.log(`[validateAndCorrectSolution] Move ${i} positions are valid`);
			correctedMoves.push(originalMove);
			totalScore += originalMove.score;
			currentGameState = currentGameState.playMove(originalMove);
		} else {
			// Need to find correct positions for this word on the current board state
			console.log(`[validateAndCorrectSolution] Move ${i} needs position correction`);
			console.log(
				`[validateAndCorrectSolution] Expected: ${originalMove.letters}, Got: ${expectedLetters}`
			);

			// Use bestWords to find the best way to play this word on the current board
			const bestWordsResult = await bestWords(currentGameState.board, 50);
			const validWord = bestWordsResult.find((word) => word.letters === originalMove.letters);

			if (validWord) {
				console.log(
					`[validateAndCorrectSolution] Found corrected positions for ${originalMove.letters}`
				);
				correctedMoves.push(validWord);
				totalScore += validWord.score;
				currentGameState = currentGameState.playMove(validWord);
			} else {
				console.error(
					`[validateAndCorrectSolution] Could not find valid positions for word: ${originalMove.letters}`
				);
				return null; // Solution cannot be corrected
			}
		}
	}

	console.log(
		`[validateAndCorrectSolution] Solution corrected successfully. Total score: ${totalScore}`
	);
	return { moves: correctedMoves, total: totalScore };
}

/**
 * Find correct positions for a word on the board, ensuring positions[i] corresponds to word[i]
 */
function findCorrectPositions(word: string, board: Board): Array<[number, number]> | null {
	const letters = word.split('');
	const positions: Array<[number, number]> = [];
	const usedPositions = new Set<string>();

	// Build a map of available tiles for each letter
	const letterTileMap: Record<
		string,
		Array<{ row: number; col: number; tile: import('../models/Tile').Tile }>
	> = {};
	for (let row = 0; row < 5; row++) {
		for (let col = 0; col < 5; col++) {
			const tile = board.getTile(row, col);
			if (tile) {
				if (!letterTileMap[tile.letter]) {
					letterTileMap[tile.letter] = [];
				}
				letterTileMap[tile.letter].push({ row, col, tile });
			}
		}
	}

	// Sort tiles by multiplier value (best first) for each letter
	for (const letter in letterTileMap) {
		letterTileMap[letter].sort((a, b) => {
			const aValue = a.tile.letterMult * a.tile.wordMult;
			const bValue = b.tile.letterMult * b.tile.wordMult;
			return bValue - aValue; // Descending order (best first)
		});
	}

	// For each letter in the word (in order), find the best available tile
	for (let i = 0; i < letters.length; i++) {
		const letter = letters[i];
		const availableTiles = letterTileMap[letter];
		if (!availableTiles || availableTiles.length === 0) {
			return null; // Letter not available on board
		}

		// Find the best unused tile for this letter
		let bestTile: { row: number; col: number; tile: import('../models/Tile').Tile } | null = null;
		for (const tileInfo of availableTiles) {
			const posKey = `${tileInfo.row},${tileInfo.col}`;
			if (!usedPositions.has(posKey)) {
				bestTile = tileInfo;
				break; // Take the first (best) available tile
			}
		}

		if (!bestTile) {
			return null; // No unused tiles available for this letter
		}

		positions.push([bestTile.row, bestTile.col]);
		usedPositions.add(`${bestTile.row},${bestTile.col}`);
	}

	return positions;
}

/**
 * Create enhanced move data by replaying the solution from the initial board state
 * This ensures that board states are captured correctly for each move
 */
async function createEnhancedMovesFromSolution(
	moves: (Word | WordInterface)[],
	initialBoard: Board
): Promise<EnhancedMove[]> {
	const enhancedMoves: EnhancedMove[] = [];
	let currentGameState = new GameState(initialBoard, 0, 0, []);

	console.log(
		'[createEnhancedMovesFromSolution] Starting replay with initial board, first tile:',
		currentGameState.board.tiles[0][0].letter
	);

	for (let i = 0; i < moves.length; i++) {
		const move = moves[i];
		// Ensure we have a Word class instance
		const wordInstance = move instanceof Word ? move : Word.fromObject(move);

		console.log(
			`[createEnhancedMovesFromSolution] Replaying move ${i}: ${wordInstance.letters} at positions:`,
			wordInstance.positions
		);

		// Capture board state before the move
		const boardBefore = currentGameState.board.tiles.map((row) =>
			row.map((tile) => tile.toObject())
		);
		console.log(
			`[createEnhancedMovesFromSolution] Board before move ${i}, first tile:`,
			boardBefore[0][0].letter
		);

		// Verify that the word can be played on the current board state
		const expectedLetters = wordInstance.positions
			.map(([row, col]) => {
				const tile = currentGameState.board.getTile(row, col);
				return tile ? tile.letter : '?';
			})
			.join('');

		console.log(
			`[createEnhancedMovesFromSolution] Expected letters at positions: ${expectedLetters}, word letters: ${wordInstance.letters}`
		);

		if (expectedLetters !== wordInstance.letters) {
			console.warn(
				`[createEnhancedMovesFromSolution] POSITION MISMATCH! Word ${wordInstance.letters} positions ${JSON.stringify(wordInstance.positions)} don't match current board state (expected: ${expectedLetters}). Attempting to fix positions...`
			);

			// Try to fix the positions by finding correct positions for the word
			const correctedPositions = findCorrectPositions(wordInstance.letters, currentGameState.board);
			if (correctedPositions) {
				console.log(
					`[createEnhancedMovesFromSolution] Fixed positions for ${wordInstance.letters}: ${JSON.stringify(correctedPositions)}`
				);
				// Create a corrected word instance
				wordInstance = new Word(wordInstance.letters, correctedPositions, wordInstance.score);
			} else {
				console.error(
					`[createEnhancedMovesFromSolution] Could not find valid positions for word ${wordInstance.letters} on current board`
				);
			}
		}

		// Play the move to get the new game state
		try {
			const newGameState = currentGameState.playMove(wordInstance);
			const boardAfter = newGameState.board.tiles.map((row) => row.map((tile) => tile.toObject()));

			console.log(
				`[createEnhancedMovesFromSolution] Board after move ${i}, first tile:`,
				boardAfter[0][0].letter
			);

			// Create enhanced move with correct board states
			const enhancedMove = wordToEnhancedMove(wordInstance, boardBefore, boardAfter);
			enhancedMoves.push(enhancedMove);

			// Update current game state for next iteration
			currentGameState = newGameState;
		} catch (error) {
			console.error(`[createEnhancedMovesFromSolution] Error playing move ${i}:`, error);
			// Create enhanced move without boardAfter if move fails
			const enhancedMove = wordToEnhancedMove(wordInstance, boardBefore, undefined);
			enhancedMoves.push(enhancedMove);
		}
	}

	console.log('[createEnhancedMovesFromSolution] Completed solution replay');
	return enhancedMoves;
}

/**
 * Generate successor nodes for a given game state
 */
async function generateSuccessors(
	page: Page,
	node: GameNode,
	config: SolverConfig
): Promise<GameNode[]> {
	const successors: GameNode[] = [];
	const currentState = new GameState(
		node.board,
		node.turn,
		node.total,
		node.moves.map((move) => (move instanceof Word ? move : Word.fromObject(move)))
	);

	try {
		// Get possible words for current board
		console.log(
			`[generateSuccessors] Calling bestWords with board and K=${Math.min(config.maxWordsPerPosition, 50)}`
		);
		const possibleWords = await bestWords(node.board, Math.min(config.maxWordsPerPosition, 50));
		console.log(`[generateSuccessors] bestWords returned ${possibleWords.length} words`);

		// Dynamic beam width based on turn
		const K = Math.max(60 / (node.turn + 1), 20) | 0;
		const wordsToTry = possibleWords.slice(0, K);

		for (const word of wordsToTry) {
			try {
				// Simulate playing this word
				const newState = currentState.playMove(word);

				// Calculate upper bound for the new state
				const upperBound = upperBoundForGameState(newState, toUpperBoundConfig(config));

				// Create successor node
				const successor: GameNode = {
					board: newState.board,
					turn: newState.turn,
					total: newState.total,
					moves: newState.moves,
					upperBound
				};

				successors.push(successor);
			} catch (error) {
				// Skip invalid moves
				if (config.debug) {
					console.warn(`[generateSuccessors] Invalid move: ${word.letters}`, error);
				}
			}
		}
	} catch (error) {
		console.error('[generateSuccessors] Error generating successors:', error);
	}

	return successors;
}

/**
 * Solve with actual browser interaction (for final solution execution)
 */
export async function solveDailyBoardWithExecution(
	page: Page,
	config: SolverConfig = DEFAULT_CONFIG
): Promise<BestLineResult> {
	// First, find the optimal solution
	const solution = await solveDailyBoard(page, config);

	if (config.debug) {
		console.log('[solveDailyBoardWithExecution] Executing solution:', solution);
	}

	try {
		// Execute the solution on the actual game
		for (let i = 0; i < solution.words.length; i++) {
			const wordStr = solution.words[i];

			// We need to reconstruct the word positions
			// This is a simplified approach - in practice, we'd need to track positions
			const currentBoard = await scrapeBoard(page);
			const word = findWordOnBoard(wordStr, currentBoard);

			if (word) {
				await playWord(page, word.positions);

				// Small delay between moves
				await page.waitForTimeout(1000);
			} else {
				console.warn(`[solveDailyBoardWithExecution] Could not find word on board: ${wordStr}`);
			}
		}

		return solution;
	} catch (error) {
		console.error('[solveDailyBoardWithExecution] Error executing solution:', error);
		throw error;
	}
}

/**
 * Find a word on the board (simplified implementation)
 */
function findWordOnBoard(wordStr: string, board: Board): Word | null {
	// This is a simplified implementation
	// In practice, this would use more sophisticated word finding logic

	const tiles = board.getAllTiles();
	const letters = wordStr.split('');

	// Try to find a path that spells the word
	for (const startTile of tiles) {
		if (startTile.letter === letters[0]) {
			const path = findWordPath(letters, board, startTile.row, startTile.col, []);
			if (path) {
				return new Word(wordStr, path);
			}
		}
	}

	return null;
}

/**
 * Recursively find a path that spells the given letters
 */
function findWordPath(
	letters: string[],
	board: Board,
	row: number,
	col: number,
	usedPositions: Array<[number, number]>
): Array<[number, number]> | null {
	if (letters.length === 0) {
		return [];
	}

	const tile = board.getTile(row, col);
	if (!tile || tile.letter !== letters[0]) {
		return null;
	}

	// Check if position already used
	if (usedPositions.some(([r, c]) => r === row && c === col)) {
		return null;
	}

	if (letters.length === 1) {
		return [[row, col]];
	}

	// Try adjacent positions
	const adjacent = board.getAdjacentTiles(row, col);
	for (const adjTile of adjacent) {
		const path = findWordPath(letters.slice(1), board, adjTile.row, adjTile.col, [
			...usedPositions,
			[row, col]
		]);

		if (path) {
			return [[row, col], ...path];
		}
	}

	return null;
}

/**
 * Get solver statistics for the last run
 */
export function getSolverStats(): SolverStats | null {
	// This would be stored globally in a real implementation
	return null;
}

/**
 * Benchmark solver performance
 */
export async function benchmarkSolver(
	page: Page,
	iterations: number = 5,
	config: SolverConfig = DEFAULT_CONFIG
): Promise<{
	averageTime: number;
	averageScore: number;
	averageNodesExplored: number;
	results: BestLineResult[];
}> {
	const results: BestLineResult[] = [];
	const times: number[] = [];
	const scores: number[] = [];
	const nodesExplored: number[] = [];

	for (let i = 0; i < iterations; i++) {
		const startTime = Date.now();
		const result = await solveDailyBoard(page, { ...config, debug: false });
		const endTime = Date.now();

		results.push(result);
		times.push(endTime - startTime);
		scores.push(result.total);
		// nodesExplored would need to be tracked in stats
	}

	return {
		averageTime: times.reduce((sum, time) => sum + time, 0) / times.length,
		averageScore: scores.reduce((sum, score) => sum + score, 0) / scores.length,
		averageNodesExplored: 0, // Placeholder
		results
	};
}
